"""
Comprehensive error handling and user feedback system
"""
import traceback
from enum import Enum
from typing import Optional, Dict, Any
from loguru import logger
import discord
from src.database.models import TicketStatus


class ErrorType(Enum):
    """Types of errors that can occur"""
    NETWORK_ERROR = "network_error"
    BROWSER_ERROR = "browser_error"
    FORM_ERROR = "form_error"
    CAPTCHA_ERROR = "captcha_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    VALIDATION_ERROR = "validation_error"
    DATABASE_ERROR = "database_error"
    PERMISSION_ERROR = "permission_error"
    UNKNOWN_ERROR = "unknown_error"


class ErrorHandler:
    """Centralized error handling and user feedback"""
    
    def __init__(self, bot=None):
        self.bot = bot
        self.error_messages = {
            ErrorType.NETWORK_ERROR: {
                "user": "🌐 Network connection issue. Please try again later.",
                "log": "Network error occurred during operation"
            },
            ErrorType.BROWSER_ERROR: {
                "user": "🖥️ Browser automation issue. Please try again.",
                "log": "Browser automation error"
            },
            ErrorType.FORM_ERROR: {
                "user": "📝 Form submission issue. Please check your information and try again.",
                "log": "Form interaction error"
            },
            ErrorType.CAPTCHA_ERROR: {
                "user": "🤖 CAPTCHA verification required. Please try again later.",
                "log": "CAPTCHA challenge encountered"
            },
            ErrorType.RATE_LIMIT_ERROR: {
                "user": "⏰ Rate limit reached. Please wait before trying again.",
                "log": "Rate limit exceeded"
            },
            ErrorType.VALIDATION_ERROR: {
                "user": "❌ Invalid information provided. Please check your input.",
                "log": "Data validation failed"
            },
            ErrorType.DATABASE_ERROR: {
                "user": "💾 Database issue. Please try again later.",
                "log": "Database operation failed"
            },
            ErrorType.PERMISSION_ERROR: {
                "user": "🔒 You don't have permission to perform this action.",
                "log": "Permission denied"
            },
            ErrorType.UNKNOWN_ERROR: {
                "user": "❓ An unexpected error occurred. Please try again later.",
                "log": "Unknown error occurred"
            }
        }
    
    async def handle_error(
        self,
        error: Exception,
        error_type: ErrorType = ErrorType.UNKNOWN_ERROR,
        context: Optional[Dict[str, Any]] = None,
        user: Optional[discord.User] = None,
        ticket_id: Optional[str] = None
    ) -> str:
        """
        Handle an error with logging and user notification
        
        Args:
            error: The exception that occurred
            error_type: Type of error for categorization
            context: Additional context information
            user: Discord user to notify
            ticket_id: Associated ticket ID if applicable
            
        Returns:
            User-friendly error message
        """
        
        # Prepare context information
        context_info = context or {}
        context_info.update({
            "error_type": error_type.value,
            "error_class": error.__class__.__name__,
            "user_id": user.id if user else None,
            "ticket_id": ticket_id
        })
        
        # Log the error
        error_msg = self.error_messages[error_type]["log"]
        logger.error(
            f"{error_msg}: {str(error)}\n"
            f"Context: {context_info}\n"
            f"Traceback: {traceback.format_exc()}"
        )
        
        # Update ticket status if applicable
        if ticket_id and self.bot:
            try:
                await self.bot.database.update_ticket_status(
                    ticket_id,
                    TicketStatus.FAILED,
                    f"{error_type.value}: {str(error)}"
                )
            except Exception as db_error:
                logger.error(f"Failed to update ticket status: {db_error}")
        
        # Send user notification
        user_message = self.error_messages[error_type]["user"]
        if user:
            try:
                await self.notify_user(user, user_message, ticket_id)
            except Exception as notify_error:
                logger.error(f"Failed to notify user: {notify_error}")
        
        return user_message
    
    async def notify_user(
        self,
        user: discord.User,
        message: str,
        ticket_id: Optional[str] = None
    ):
        """Send error notification to user"""
        try:
            embed = discord.Embed(
                title="❌ Error Occurred",
                description=message,
                color=discord.Color.red()
            )
            
            if ticket_id:
                embed.add_field(
                    name="Ticket ID",
                    value=ticket_id,
                    inline=True
                )
            
            embed.add_field(
                name="Next Steps",
                value="• Try again later\n• Contact an administrator if the issue persists",
                inline=False
            )
            
            await user.send(embed=embed)
            
        except discord.Forbidden:
            logger.warning(f"Could not DM user {user.id} about error")
        except Exception as e:
            logger.error(f"Error sending notification to user: {e}")
    
    def classify_error(self, error: Exception) -> ErrorType:
        """Classify an error based on its type and message"""
        error_str = str(error).lower()
        error_class = error.__class__.__name__.lower()
        
        # Network-related errors
        if any(keyword in error_str for keyword in [
            "network", "connection", "timeout", "dns", "unreachable"
        ]) or any(keyword in error_class for keyword in [
            "timeout", "connection", "network"
        ]):
            return ErrorType.NETWORK_ERROR
        
        # Browser/Playwright errors
        if any(keyword in error_class for keyword in [
            "playwright", "browser", "page"
        ]) or any(keyword in error_str for keyword in [
            "browser", "playwright", "page closed"
        ]):
            return ErrorType.BROWSER_ERROR
        
        # Form interaction errors
        if any(keyword in error_str for keyword in [
            "element not found", "selector", "form", "input"
        ]):
            return ErrorType.FORM_ERROR
        
        # CAPTCHA errors
        if any(keyword in error_str for keyword in [
            "captcha", "recaptcha", "hcaptcha", "verification"
        ]):
            return ErrorType.CAPTCHA_ERROR
        
        # Rate limiting errors
        if any(keyword in error_str for keyword in [
            "rate limit", "too many requests", "429"
        ]):
            return ErrorType.RATE_LIMIT_ERROR
        
        # Validation errors
        if any(keyword in error_class for keyword in [
            "validation", "value"
        ]) or any(keyword in error_str for keyword in [
            "invalid", "required", "validation"
        ]):
            return ErrorType.VALIDATION_ERROR
        
        # Database errors
        if any(keyword in error_class for keyword in [
            "sqlite", "database", "db"
        ]):
            return ErrorType.DATABASE_ERROR
        
        # Permission errors
        if any(keyword in error_str for keyword in [
            "permission", "forbidden", "unauthorized", "403"
        ]):
            return ErrorType.PERMISSION_ERROR
        
        return ErrorType.UNKNOWN_ERROR
    
    async def handle_exception(
        self,
        error: Exception,
        context: Optional[Dict[str, Any]] = None,
        user: Optional[discord.User] = None,
        ticket_id: Optional[str] = None
    ) -> str:
        """
        Convenience method to handle an exception with automatic classification
        """
        error_type = self.classify_error(error)
        return await self.handle_error(
            error=error,
            error_type=error_type,
            context=context,
            user=user,
            ticket_id=ticket_id
        )


# Global error handler instance
error_handler = ErrorHandler()
