#!/usr/bin/env python3
"""
Test runner for MC-Ticketer
"""
import sys
import subprocess
import argparse
from pathlib import Path


def run_command(command, description):
    """Run a command and return success status"""
    print(f"\n{'='*50}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(command)}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        print(f"✅ {description} - PASSED")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - FAILED")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False


def main():
    parser = argparse.ArgumentParser(description="Run MC-Ticketer tests")
    parser.add_argument("--unit", action="store_true", help="Run unit tests only")
    parser.add_argument("--integration", action="store_true", help="Run integration tests only")
    parser.add_argument("--coverage", action="store_true", help="Run with coverage report")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--fast", action="store_true", help="Skip slow tests")
    
    args = parser.parse_args()
    
    # Ensure we're in the project root
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root / "src"))
    
    print("🎫 MC-Ticketer Test Runner")
    print(f"Project root: {project_root}")
    
    # Check if pytest is available
    try:
        import pytest
        print(f"✅ pytest version: {pytest.__version__}")
    except ImportError:
        print("❌ pytest not found. Please install: pip install pytest pytest-asyncio pytest-cov")
        return 1
    
    # Build test command
    base_cmd = ["python", "-m", "pytest"]
    
    if args.verbose:
        base_cmd.append("-v")
    
    if args.coverage:
        base_cmd.extend(["--cov=src", "--cov-report=html", "--cov-report=term"])
    
    if args.fast:
        base_cmd.extend(["-m", "not slow"])
    
    success_count = 0
    total_tests = 0
    
    # Determine which tests to run
    if args.unit and not args.integration:
        test_files = [
            "tests/test_database.py",
            "tests/test_rate_limiter.py", 
            "tests/test_error_handler.py"
        ]
        test_type = "Unit Tests"
    elif args.integration and not args.unit:
        test_files = ["tests/test_integration.py"]
        test_type = "Integration Tests"
    else:
        test_files = ["tests/"]
        test_type = "All Tests"
    
    print(f"\n🧪 Running {test_type}")
    
    for test_target in test_files:
        if Path(test_target).exists():
            total_tests += 1
            cmd = base_cmd + [test_target]
            if run_command(cmd, f"Testing {test_target}"):
                success_count += 1
        else:
            print(f"⚠️  Test file not found: {test_target}")
    
    # Run additional checks
    print(f"\n🔍 Running Additional Checks")
    
    # Check code style (if flake8 is available)
    try:
        import flake8
        total_tests += 1
        if run_command(["python", "-m", "flake8", "src/", "--max-line-length=100"], "Code Style Check"):
            success_count += 1
    except ImportError:
        print("ℹ️  flake8 not available, skipping style check")
    
    # Check imports
    total_tests += 1
    if run_command(["python", "-c", "import src.config; print('✅ Config import OK')"], "Import Check"):
        success_count += 1
    
    # Summary
    print(f"\n{'='*50}")
    print(f"📊 Test Summary")
    print(f"{'='*50}")
    print(f"Total tests: {total_tests}")
    print(f"Passed: {success_count}")
    print(f"Failed: {total_tests - success_count}")
    
    if success_count == total_tests:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
