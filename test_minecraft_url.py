#!/usr/bin/env python3
"""
Test script to verify Minecraft support URL accessibility
"""
import asyncio
import sys
from playwright.async_api import async_playwright


async def test_minecraft_url():
    """Test if we can access the Minecraft support URL"""
    print("🧪 Testing Minecraft Support URL Access")
    print("=" * 50)
    
    ticket_url = "https://help.minecraft.net/hc/en-us/requests/new"
    
    async with async_playwright() as p:
        try:
            # Launch browser
            print("🚀 Launching browser...")
            browser = await p.chromium.launch(
                headless=False,  # Show browser for debugging
                args=[
                    '--no-sandbox',
                    '--disable-http2',
                    '--disable-dev-shm-usage',
                    '--disable-gpu'
                ]
            )
            
            # Create page
            context = await browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )
            page = await context.new_page()
            
            print(f"🌐 Navigating to: {ticket_url}")
            
            # Try to navigate
            try:
                await page.goto(ticket_url, timeout=30000)
                print("✅ Successfully navigated to URL")
                
                # Get page info
                title = await page.title()
                url = page.url
                
                print(f"📄 Page title: {title}")
                print(f"🔗 Current URL: {url}")
                
                # Check for form elements
                print("\n🔍 Looking for form elements...")
                
                form_selectors = [
                    'form',
                    'input[type="email"]',
                    'textarea',
                    'input[type="text"]',
                    'select'
                ]
                
                found_elements = []
                for selector in form_selectors:
                    try:
                        elements = await page.query_selector_all(selector)
                        if elements:
                            found_elements.append(f"{selector}: {len(elements)} found")
                    except:
                        pass
                
                if found_elements:
                    print("✅ Form elements found:")
                    for element in found_elements:
                        print(f"  - {element}")
                else:
                    print("❌ No form elements found")
                
                # Take a screenshot for debugging
                await page.screenshot(path="minecraft_support_page.png")
                print("📸 Screenshot saved as 'minecraft_support_page.png'")
                
                # Wait a bit to see the page
                print("\n⏳ Waiting 5 seconds to observe the page...")
                await asyncio.sleep(5)
                
            except Exception as e:
                print(f"❌ Navigation failed: {e}")
                
                # Try alternative URLs
                alternative_urls = [
                    "https://help.minecraft.net/",
                    "https://help.minecraft.net/hc/en-us",
                    "https://www.minecraft.net/en-us/help"
                ]
                
                print("\n🔄 Trying alternative URLs...")
                for alt_url in alternative_urls:
                    try:
                        print(f"  Trying: {alt_url}")
                        await page.goto(alt_url, timeout=15000)
                        title = await page.title()
                        print(f"  ✅ Success! Title: {title}")
                        break
                    except Exception as alt_e:
                        print(f"  ❌ Failed: {alt_e}")
            
            await browser.close()
            
        except Exception as e:
            print(f"❌ Browser setup failed: {e}")
            return False
    
    return True


if __name__ == "__main__":
    try:
        asyncio.run(test_minecraft_url())
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        sys.exit(1)
