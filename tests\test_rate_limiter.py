"""
Unit tests for rate limiting functionality
"""
import pytest
from datetime import date
from src.utils.rate_limiter import RateLimiter
from src.config import settings


@pytest.fixture
def rate_limiter():
    """Create a rate limiter instance"""
    return RateLimiter()


@pytest.mark.asyncio
async def test_user_within_limits(rate_limiter):
    """Test user within rate limits"""
    user_id = 12345
    
    # First request should be allowed
    result = await rate_limiter.check_user_limit(user_id)
    assert result is True
    
    # Check remaining count
    remaining = rate_limiter.get_user_remaining(user_id)
    assert remaining == settings.max_tickets_per_user_per_day - 1


@pytest.mark.asyncio
async def test_user_exceeds_limits(rate_limiter):
    """Test user exceeding rate limits"""
    user_id = 67890
    
    # Use up all allowed tickets
    for i in range(settings.max_tickets_per_user_per_day):
        result = await rate_limiter.check_user_limit(user_id)
        assert result is True
    
    # Next request should be denied
    result = await rate_limiter.check_user_limit(user_id)
    assert result is False
    
    # Remaining should be 0
    remaining = rate_limiter.get_user_remaining(user_id)
    assert remaining == 0


@pytest.mark.asyncio
async def test_daily_reset(rate_limiter):
    """Test daily reset functionality"""
    user_id = 11111
    
    # Use up all tickets
    for i in range(settings.max_tickets_per_user_per_day):
        await rate_limiter.check_user_limit(user_id)
    
    # Should be at limit
    result = await rate_limiter.check_user_limit(user_id)
    assert result is False
    
    # Simulate new day by manually changing the date
    rate_limiter.user_counts[user_id]['date'] = date(2020, 1, 1)
    
    # Should be allowed again
    result = await rate_limiter.check_user_limit(user_id)
    assert result is True


@pytest.mark.asyncio
async def test_multiple_users(rate_limiter):
    """Test multiple users independently"""
    user1 = 11111
    user2 = 22222
    
    # Both users should be able to use their full quota
    for i in range(settings.max_tickets_per_user_per_day):
        result1 = await rate_limiter.check_user_limit(user1)
        result2 = await rate_limiter.check_user_limit(user2)
        assert result1 is True
        assert result2 is True
    
    # Both should be at limit
    result1 = await rate_limiter.check_user_limit(user1)
    result2 = await rate_limiter.check_user_limit(user2)
    assert result1 is False
    assert result2 is False


if __name__ == "__main__":
    pytest.main([__file__])
