"""
Integration tests for MC-Ticketer
"""
import pytest
import asyncio
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch
from src.database.models import Database, TicketData, TicketStatus
from src.bot.client import MCTicketerBot
from src.scraper.minecraft_scraper import MinecraftScraper
from src.utils.rate_limiter import RateLimiter


@pytest.fixture
async def test_bot():
    """Create a test bot instance"""
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
        db_path = Path(tmp_file.name)
    
    # Mock the bot initialization
    bot = MCTicketerBot()
    bot.database = Database()
    bot.database.db_path = db_path
    await bot.database.initialize()
    
    yield bot
    
    await bot.database.close()
    db_path.unlink()


@pytest.mark.asyncio
async def test_full_ticket_workflow(test_bot):
    """Test the complete ticket creation and processing workflow"""
    # Create test ticket data
    ticket_data = TicketData(
        user_id=12345,
        ticket_type="Technical Issue",
        subject="Integration test ticket",
        description="This is a test ticket for integration testing",
        email="<EMAIL>",
        minecraft_username="TestUser"
    )
    
    # Create ticket in database
    ticket_id = await test_bot.database.create_ticket(ticket_data)
    assert ticket_id is not None
    
    # Verify ticket was created with correct status
    created_ticket = await test_bot.database.get_ticket(ticket_id)
    assert created_ticket.status == TicketStatus.PENDING
    
    # Simulate status updates through the workflow
    await test_bot.database.update_ticket_status(ticket_id, TicketStatus.SUBMITTED)
    updated_ticket = await test_bot.database.get_ticket(ticket_id)
    assert updated_ticket.status == TicketStatus.SUBMITTED
    
    # Simulate completion
    await test_bot.database.update_ticket_status(ticket_id, TicketStatus.COMPLETED)
    final_ticket = await test_bot.database.get_ticket(ticket_id)
    assert final_ticket.status == TicketStatus.COMPLETED


@pytest.mark.asyncio
async def test_rate_limiting_integration(test_bot):
    """Test rate limiting integration with database"""
    rate_limiter = RateLimiter()
    user_id = 67890
    
    # Create tickets up to the limit
    from src.config import settings
    for i in range(settings.max_tickets_per_user_per_day):
        # Check rate limit
        allowed = await rate_limiter.check_user_limit(user_id)
        assert allowed is True
        
        # Create ticket
        ticket_data = TicketData(
            user_id=user_id,
            ticket_type="Test",
            subject=f"Test ticket {i}",
            description="Rate limit test",
            email="<EMAIL>"
        )
        await test_bot.database.create_ticket(ticket_data)
    
    # Next request should be denied
    allowed = await rate_limiter.check_user_limit(user_id)
    assert allowed is False
    
    # Verify we have the expected number of tickets
    user_tickets = await test_bot.database.get_user_tickets(user_id)
    assert len(user_tickets) == settings.max_tickets_per_user_per_day


@pytest.mark.asyncio
async def test_scraper_initialization():
    """Test scraper initialization and cleanup"""
    scraper = MinecraftScraper()
    
    # Test context manager
    async with scraper:
        assert scraper.browser is not None
        assert scraper.page is not None
    
    # After context exit, browser should be closed
    # Note: We can't easily test this without actually running the browser


@pytest.mark.asyncio
async def test_error_handling_integration(test_bot):
    """Test error handling integration across components"""
    from src.utils.error_handler import ErrorHandler, ErrorType
    
    error_handler = ErrorHandler(bot=test_bot)
    
    # Create a test ticket
    ticket_data = TicketData(
        user_id=11111,
        ticket_type="Error Test",
        subject="Error handling test",
        description="Testing error handling",
        email="<EMAIL>"
    )
    
    ticket_id = await test_bot.database.create_ticket(ticket_data)
    
    # Simulate an error
    test_error = Exception("Test error for integration")
    
    await error_handler.handle_error(
        error=test_error,
        error_type=ErrorType.NETWORK_ERROR,
        ticket_id=ticket_id
    )
    
    # Verify ticket status was updated to failed
    updated_ticket = await test_bot.database.get_ticket(ticket_id)
    assert updated_ticket.status == TicketStatus.FAILED
    assert "network_error" in updated_ticket.error_message


@pytest.mark.asyncio
async def test_database_statistics_integration(test_bot):
    """Test statistics calculation with real data"""
    # Create tickets with different statuses
    test_data = [
        (TicketStatus.COMPLETED, "<EMAIL>"),
        (TicketStatus.FAILED, "<EMAIL>"),
        (TicketStatus.PENDING, "<EMAIL>"),
        (TicketStatus.SUBMITTED, "<EMAIL>")
    ]
    
    created_tickets = []
    for status, email in test_data:
        ticket_data = TicketData(
            user_id=22222,
            ticket_type="Stats Test",
            subject="Statistics test",
            description="Testing statistics",
            email=email
        )
        
        ticket_id = await test_bot.database.create_ticket(ticket_data)
        created_tickets.append(ticket_id)
        
        # Update status if not pending
        if status != TicketStatus.PENDING:
            await test_bot.database.update_ticket_status(ticket_id, status)
    
    # Get statistics
    stats = await test_bot.database.get_statistics()
    
    assert stats['total_tickets'] >= 4
    assert stats['successful_tickets'] >= 1
    assert stats['failed_tickets'] >= 1
    assert stats['active_users'] >= 1


@pytest.mark.asyncio
async def test_concurrent_ticket_creation(test_bot):
    """Test concurrent ticket creation"""
    async def create_ticket(user_id, ticket_num):
        ticket_data = TicketData(
            user_id=user_id,
            ticket_type="Concurrent Test",
            subject=f"Concurrent ticket {ticket_num}",
            description="Testing concurrent creation",
            email=f"concurrent{ticket_num}@test.com"
        )
        return await test_bot.database.create_ticket(ticket_data)
    
    # Create multiple tickets concurrently
    tasks = [create_ticket(33333, i) for i in range(5)]
    ticket_ids = await asyncio.gather(*tasks)
    
    # Verify all tickets were created
    assert len(ticket_ids) == 5
    assert len(set(ticket_ids)) == 5  # All IDs should be unique
    
    # Verify tickets exist in database
    for ticket_id in ticket_ids:
        ticket = await test_bot.database.get_ticket(ticket_id)
        assert ticket is not None
        assert ticket.user_id == 33333


@pytest.mark.asyncio
async def test_user_summary_integration(test_bot):
    """Test user summary functionality"""
    # Create tickets for multiple users
    users_data = [
        (44444, 3),  # User 44444 with 3 tickets
        (55555, 2),  # User 55555 with 2 tickets
        (66666, 1),  # User 66666 with 1 ticket
    ]
    
    for user_id, ticket_count in users_data:
        for i in range(ticket_count):
            ticket_data = TicketData(
                user_id=user_id,
                ticket_type="Summary Test",
                subject=f"Summary test {i}",
                description="Testing user summary",
                email=f"user{user_id}@test.com"
            )
            await test_bot.database.create_ticket(ticket_data)
    
    # Get user summary
    summary = await test_bot.database.get_user_summary()
    
    # Find our test users in the summary
    test_users = {user['user_id']: user['ticket_count'] for user in summary 
                  if user['user_id'] in [44444, 55555, 66666]}
    
    assert test_users[44444] == 3
    assert test_users[55555] == 2
    assert test_users[66666] == 1


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
