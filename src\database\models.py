"""
Database models and data structures for MC-Ticketer
"""
import uuid
from datetime import datetime
from enum import Enum
from typing import Optional
from pydantic import BaseModel, Field
import aiosqlite
from src.config import settings, DATA_DIR


class TicketStatus(Enum):
    """Ticket status enumeration"""
    PENDING = "pending"
    SUBMITTED = "submitted"
    COMPLETED = "completed"
    FAILED = "failed"


class TicketData(BaseModel):
    """Data model for support tickets"""
    ticket_id: Optional[str] = None
    user_id: int
    ticket_type: str
    subject: str
    description: str
    email: str
    minecraft_username: Optional[str] = None
    status: TicketStatus = TicketStatus.PENDING
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    submission_attempts: int = 0
    error_message: Optional[str] = None


class Database:
    """Database manager for MC-Ticketer"""
    
    def __init__(self):
        self.db_path = DATA_DIR / "tickets.db"
        self.connection = None
    
    async def initialize(self):
        """Initialize database and create tables"""
        self.connection = await aiosqlite.connect(self.db_path)
        await self.create_tables()
    
    async def create_tables(self):
        """Create database tables"""
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS tickets (
                ticket_id TEXT PRIMARY KEY,
                user_id INTEGER NOT NULL,
                ticket_type TEXT NOT NULL,
                subject TEXT NOT NULL,
                description TEXT NOT NULL,
                email TEXT NOT NULL,
                minecraft_username TEXT,
                status TEXT NOT NULL,
                created_at TIMESTAMP NOT NULL,
                updated_at TIMESTAMP NOT NULL,
                submission_attempts INTEGER DEFAULT 0,
                error_message TEXT
            )
        """)
        
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS user_limits (
                user_id INTEGER PRIMARY KEY,
                daily_count INTEGER DEFAULT 0,
                last_reset_date DATE NOT NULL
            )
        """)
        
        await self.connection.commit()
    
    async def create_ticket(self, ticket_data: TicketData) -> str:
        """Create a new ticket and return its ID"""
        ticket_id = str(uuid.uuid4())[:8].upper()
        
        await self.connection.execute("""
            INSERT INTO tickets (
                ticket_id, user_id, ticket_type, subject, description,
                email, minecraft_username, status, created_at, updated_at,
                submission_attempts, error_message
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            ticket_id,
            ticket_data.user_id,
            ticket_data.ticket_type,
            ticket_data.subject,
            ticket_data.description,
            ticket_data.email,
            ticket_data.minecraft_username,
            ticket_data.status.value,
            ticket_data.created_at,
            ticket_data.updated_at,
            ticket_data.submission_attempts,
            ticket_data.error_message
        ))
        
        await self.connection.commit()
        return ticket_id
    
    async def get_ticket(self, ticket_id: str) -> Optional[TicketData]:
        """Get a ticket by ID"""
        cursor = await self.connection.execute(
            "SELECT * FROM tickets WHERE ticket_id = ?",
            (ticket_id,)
        )
        row = await cursor.fetchone()
        
        if not row:
            return None
        
        return TicketData(
            ticket_id=row[0],
            user_id=row[1],
            ticket_type=row[2],
            subject=row[3],
            description=row[4],
            email=row[5],
            minecraft_username=row[6],
            status=TicketStatus(row[7]),
            created_at=datetime.fromisoformat(row[8]),
            updated_at=datetime.fromisoformat(row[9]),
            submission_attempts=row[10],
            error_message=row[11]
        )
    
    async def update_ticket_status(self, ticket_id: str, status: TicketStatus, error_message: str = None):
        """Update ticket status"""
        await self.connection.execute("""
            UPDATE tickets 
            SET status = ?, updated_at = ?, error_message = ?
            WHERE ticket_id = ?
        """, (status.value, datetime.now(), error_message, ticket_id))
        
        await self.connection.commit()
    
    async def get_user_tickets(self, user_id: int) -> list[TicketData]:
        """Get all tickets for a user"""
        cursor = await self.connection.execute(
            "SELECT * FROM tickets WHERE user_id = ? ORDER BY created_at DESC",
            (user_id,)
        )
        rows = await cursor.fetchall()
        
        tickets = []
        for row in rows:
            tickets.append(TicketData(
                ticket_id=row[0],
                user_id=row[1],
                ticket_type=row[2],
                subject=row[3],
                description=row[4],
                email=row[5],
                minecraft_username=row[6],
                status=TicketStatus(row[7]),
                created_at=datetime.fromisoformat(row[8]),
                updated_at=datetime.fromisoformat(row[9]),
                submission_attempts=row[10],
                error_message=row[11]
            ))
        
        return tickets
    
    async def get_statistics(self) -> dict:
        """Get system statistics"""
        stats = {}
        
        # Total tickets
        cursor = await self.connection.execute("SELECT COUNT(*) FROM tickets")
        stats['total_tickets'] = (await cursor.fetchone())[0]
        
        # Successful tickets
        cursor = await self.connection.execute(
            "SELECT COUNT(*) FROM tickets WHERE status = ?",
            (TicketStatus.COMPLETED.value,)
        )
        stats['successful_tickets'] = (await cursor.fetchone())[0]
        
        # Failed tickets
        cursor = await self.connection.execute(
            "SELECT COUNT(*) FROM tickets WHERE status = ?",
            (TicketStatus.FAILED.value,)
        )
        stats['failed_tickets'] = (await cursor.fetchone())[0]
        
        # Active users
        cursor = await self.connection.execute("SELECT COUNT(DISTINCT user_id) FROM tickets")
        stats['active_users'] = (await cursor.fetchone())[0]
        
        # Today's tickets
        today = datetime.now().date()
        cursor = await self.connection.execute(
            "SELECT COUNT(*) FROM tickets WHERE DATE(created_at) = ?",
            (today,)
        )
        stats['todays_tickets'] = (await cursor.fetchone())[0]
        
        return stats
    
    async def get_user_summary(self) -> list[dict]:
        """Get user summary for admin"""
        cursor = await self.connection.execute("""
            SELECT 
                user_id,
                COUNT(*) as ticket_count,
                MAX(created_at) as last_ticket
            FROM tickets 
            GROUP BY user_id 
            ORDER BY ticket_count DESC
        """)
        rows = await cursor.fetchall()
        
        return [
            {
                'user_id': row[0],
                'ticket_count': row[1],
                'last_ticket': row[2]
            }
            for row in rows
        ]
    
    async def close(self):
        """Close database connection"""
        if self.connection:
            await self.connection.close()
