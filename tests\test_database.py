"""
Unit tests for database functionality
"""
import pytest
import asyncio
import tempfile
from pathlib import Path
from datetime import datetime
from src.database.models import Database, TicketData, TicketStatus


@pytest.fixture
async def test_database():
    """Create a temporary test database"""
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
        db_path = Path(tmp_file.name)
    
    # Override the database path for testing
    db = Database()
    db.db_path = db_path
    await db.initialize()
    
    yield db
    
    await db.close()
    db_path.unlink()  # Clean up


@pytest.mark.asyncio
async def test_create_ticket(test_database):
    """Test ticket creation"""
    ticket_data = TicketData(
        user_id=12345,
        ticket_type="Technical Issue",
        subject="Test ticket",
        description="This is a test ticket",
        email="<EMAIL>",
        minecraft_username="TestUser"
    )
    
    ticket_id = await test_database.create_ticket(ticket_data)
    
    assert ticket_id is not None
    assert len(ticket_id) == 8  # Should be 8 characters
    assert ticket_id.isupper()  # Should be uppercase


@pytest.mark.asyncio
async def test_get_ticket(test_database):
    """Test ticket retrieval"""
    # Create a ticket first
    ticket_data = TicketData(
        user_id=12345,
        ticket_type="Account Issue",
        subject="Test retrieval",
        description="Testing ticket retrieval",
        email="<EMAIL>"
    )
    
    ticket_id = await test_database.create_ticket(ticket_data)
    
    # Retrieve the ticket
    retrieved_ticket = await test_database.get_ticket(ticket_id)
    
    assert retrieved_ticket is not None
    assert retrieved_ticket.ticket_id == ticket_id
    assert retrieved_ticket.user_id == 12345
    assert retrieved_ticket.subject == "Test retrieval"
    assert retrieved_ticket.status == TicketStatus.PENDING


@pytest.mark.asyncio
async def test_update_ticket_status(test_database):
    """Test ticket status updates"""
    # Create a ticket
    ticket_data = TicketData(
        user_id=12345,
        ticket_type="Billing",
        subject="Test status update",
        description="Testing status updates",
        email="<EMAIL>"
    )
    
    ticket_id = await test_database.create_ticket(ticket_data)
    
    # Update status
    await test_database.update_ticket_status(
        ticket_id, 
        TicketStatus.COMPLETED,
        "Test completion"
    )
    
    # Verify update
    updated_ticket = await test_database.get_ticket(ticket_id)
    assert updated_ticket.status == TicketStatus.COMPLETED
    assert updated_ticket.error_message == "Test completion"


@pytest.mark.asyncio
async def test_get_user_tickets(test_database):
    """Test retrieving user tickets"""
    user_id = 67890
    
    # Create multiple tickets for the user
    for i in range(3):
        ticket_data = TicketData(
            user_id=user_id,
            ticket_type="Test Type",
            subject=f"Test ticket {i}",
            description=f"Description {i}",
            email="<EMAIL>"
        )
        await test_database.create_ticket(ticket_data)
    
    # Retrieve user tickets
    user_tickets = await test_database.get_user_tickets(user_id)
    
    assert len(user_tickets) == 3
    assert all(ticket.user_id == user_id for ticket in user_tickets)


@pytest.mark.asyncio
async def test_get_statistics(test_database):
    """Test statistics retrieval"""
    # Create some test tickets with different statuses
    statuses = [TicketStatus.COMPLETED, TicketStatus.FAILED, TicketStatus.PENDING]
    
    for i, status in enumerate(statuses):
        ticket_data = TicketData(
            user_id=i + 1,
            ticket_type="Test",
            subject=f"Test {i}",
            description="Test description",
            email=f"test{i}@example.com",
            status=status
        )
        ticket_id = await test_database.create_ticket(ticket_data)
        
        # Update status if not pending
        if status != TicketStatus.PENDING:
            await test_database.update_ticket_status(ticket_id, status)
    
    # Get statistics
    stats = await test_database.get_statistics()
    
    assert stats['total_tickets'] == 3
    assert stats['active_users'] == 3
    assert stats['successful_tickets'] >= 1
    assert stats['failed_tickets'] >= 1


if __name__ == "__main__":
    pytest.main([__file__])
