"""
Unit tests for error handling functionality
"""
import pytest
from unittest.mock import Async<PERSON>ock, MagicMock
from src.utils.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorType


@pytest.fixture
def error_handler():
    """Create an error handler instance"""
    mock_bot = MagicMock()
    mock_bot.database = AsyncMock()
    return <PERSON>rror<PERSON><PERSON><PERSON>(bot=mock_bot)


@pytest.mark.asyncio
async def test_classify_network_error(error_handler):
    """Test network error classification"""
    network_errors = [
        ConnectionError("Connection failed"),
        TimeoutError("Request timed out"),
        Exception("Network unreachable")
    ]
    
    for error in network_errors:
        error_type = error_handler.classify_error(error)
        assert error_type == ErrorType.NETWORK_ERROR


@pytest.mark.asyncio
async def test_classify_validation_error(error_handler):
    """Test validation error classification"""
    validation_errors = [
        ValueError("Invalid email format"),
        Exception("Required field missing"),
        Exception("Validation failed")
    ]
    
    for error in validation_errors:
        error_type = error_handler.classify_error(error)
        assert error_type == ErrorType.VALIDATION_ERROR


@pytest.mark.asyncio
async def test_classify_unknown_error(error_handler):
    """Test unknown error classification"""
    unknown_error = Exception("Some random error")
    error_type = error_handler.classify_error(unknown_error)
    assert error_type == ErrorType.UNKNOWN_ERROR


@pytest.mark.asyncio
async def test_handle_error_with_ticket_id(error_handler):
    """Test error handling with ticket ID"""
    error = Exception("Test error")
    ticket_id = "TEST123"
    
    # Mock the database update
    error_handler.bot.database.update_ticket_status = AsyncMock()
    
    result = await error_handler.handle_error(
        error=error,
        error_type=ErrorType.NETWORK_ERROR,
        ticket_id=ticket_id
    )
    
    # Should return user-friendly message
    assert "Network connection issue" in result
    
    # Should have called database update
    error_handler.bot.database.update_ticket_status.assert_called_once()


@pytest.mark.asyncio
async def test_handle_exception_auto_classify(error_handler):
    """Test automatic error classification"""
    error = ConnectionError("Network failed")
    
    result = await error_handler.handle_exception(error)
    
    # Should classify as network error and return appropriate message
    assert "Network connection issue" in result


if __name__ == "__main__":
    pytest.main([__file__])
