"""
Discord bot client for MC-Ticketer
"""
import discord
from discord.ext import commands
from loguru import logger
from src.config import settings
from src.database.models import Database
from src.bot.cogs.ticket_commands import TicketCommands
from src.bot.cogs.admin_commands import AdminCommands
from src.utils.error_handler import ErrorHandler


class MCTicketerBot(commands.Bot):
    """Main Discord bot class for MC-Ticketer"""
    
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = True
        intents.guilds = True
        intents.members = True
        
        super().__init__(
            command_prefix=settings.command_prefix,
            intents=intents,
            help_command=None  # We'll create a custom help command
        )
        
        self.database = None
        self.error_handler = ErrorHandler(self)
    
    async def setup_hook(self):
        """Called when the bot is starting up"""
        logger.info("Setting up MC-Ticketer bot...")
        
        # Initialize database
        self.database = Database()
        await self.database.initialize()
        
        # Load cogs
        await self.add_cog(TicketCommands(self))
        await self.add_cog(AdminCommands(self))
        
        logger.info("Bot setup complete")
    
    async def on_ready(self):
        """Called when the bot is ready"""
        logger.info(f"Bot logged in as {self.user} (ID: {self.user.id})")
        logger.info(f"Connected to {len(self.guilds)} guilds")
        
        # Set bot status
        activity = discord.Activity(
            type=discord.ActivityType.watching,
            name="for Minecraft support tickets"
        )
        await self.change_presence(activity=activity)
    
    async def on_command_error(self, ctx, error):
        """Global error handler for commands"""
        if isinstance(error, commands.CommandNotFound):
            return  # Ignore unknown commands
        
        elif isinstance(error, commands.MissingPermissions):
            await ctx.send("❌ You don't have permission to use this command.")
        
        elif isinstance(error, commands.MissingRequiredArgument):
            await ctx.send(f"❌ Missing required argument: `{error.param.name}`")
        
        elif isinstance(error, commands.BadArgument):
            await ctx.send("❌ Invalid argument provided.")
        
        elif isinstance(error, commands.CommandOnCooldown):
            await ctx.send(f"⏰ Command on cooldown. Try again in {error.retry_after:.1f} seconds.")
        
        else:
            logger.error(f"Unhandled command error: {error}")
            await ctx.send("❌ An unexpected error occurred. Please try again later.")
    
    async def close(self):
        """Clean shutdown"""
        logger.info("Shutting down bot...")
        if self.database:
            await self.database.close()
        await super().close()
