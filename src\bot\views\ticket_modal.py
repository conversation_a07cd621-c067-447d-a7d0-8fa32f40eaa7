"""
Discord modal for ticket creation
"""
import discord
from loguru import logger
from src.database.models import TicketData, TicketStatus
from src.scraper.minecraft_scraper import MinecraftScraper


class TicketModal(discord.ui.Modal):
    """Modal for collecting ticket information from users"""
    
    def __init__(self, bot):
        super().__init__(title="Create Minecraft Support Ticket")
        self.bot = bot
        
        # Add input fields
        self.ticket_type = discord.ui.TextInput(
            label="Ticket Type",
            placeholder="e.g., Account Issues, Technical Problems, Billing",
            max_length=100,
            required=True
        )
        
        self.subject = discord.ui.TextInput(
            label="Subject",
            placeholder="Brief description of your issue",
            max_length=200,
            required=True
        )
        
        self.description = discord.ui.TextInput(
            label="Description",
            placeholder="Detailed description of your issue...",
            style=discord.TextStyle.paragraph,
            max_length=2000,
            required=True
        )
        
        self.email = discord.ui.TextInput(
            label="Email Address",
            placeholder="<EMAIL>",
            max_length=100,
            required=True
        )
        
        self.minecraft_username = discord.ui.TextInput(
            label="Minecraft Username (if applicable)",
            placeholder="Your Minecraft username",
            max_length=50,
            required=False
        )
        
        # Add all fields to the modal
        self.add_item(self.ticket_type)
        self.add_item(self.subject)
        self.add_item(self.description)
        self.add_item(self.email)
        self.add_item(self.minecraft_username)
    
    async def on_submit(self, interaction: discord.Interaction):
        """Handle modal submission"""
        await interaction.response.defer(thinking=True)
        
        try:
            # Create ticket data object
            ticket_data = TicketData(
                user_id=interaction.user.id,
                ticket_type=self.ticket_type.value,
                subject=self.subject.value,
                description=self.description.value,
                email=self.email.value,
                minecraft_username=self.minecraft_username.value or None,
                status=TicketStatus.PENDING
            )
            
            # Save ticket to database
            ticket_id = await self.bot.database.create_ticket(ticket_data)
            ticket_data.ticket_id = ticket_id
            
            # Send confirmation message
            embed = discord.Embed(
                title="🎫 Ticket Created",
                description=f"Your ticket has been created with ID: `{ticket_id}`",
                color=discord.Color.green()
            )
            
            embed.add_field(name="Type", value=ticket_data.ticket_type, inline=True)
            embed.add_field(name="Subject", value=ticket_data.subject, inline=True)
            embed.add_field(name="Status", value="⏳ Processing", inline=True)
            
            await interaction.followup.send(embed=embed)
            
            # Start ticket submission process in background
            self.bot.loop.create_task(self.submit_ticket(ticket_data, interaction.user))
            
        except Exception as e:
            logger.error(f"Error creating ticket: {e}")
            await interaction.followup.send("❌ Error creating ticket. Please try again later.")
    
    async def submit_ticket(self, ticket_data: TicketData, user: discord.User):
        """Submit ticket using web scraper"""
        try:
            logger.info(f"Starting ticket submission for {ticket_data.ticket_id}")
            
            # Update status to submitting
            await self.bot.database.update_ticket_status(
                ticket_data.ticket_id, 
                TicketStatus.SUBMITTED
            )
            
            # Initialize scraper and submit ticket
            scraper = MinecraftScraper()
            success = await scraper.submit_ticket(ticket_data)
            
            if success:
                # Update status to completed
                await self.bot.database.update_ticket_status(
                    ticket_data.ticket_id, 
                    TicketStatus.COMPLETED
                )
                
                # Send success message to user
                embed = discord.Embed(
                    title="✅ Ticket Submitted Successfully",
                    description=f"Your ticket `{ticket_data.ticket_id}` has been submitted to Minecraft support.",
                    color=discord.Color.green()
                )
                
                try:
                    await user.send(embed=embed)
                except discord.Forbidden:
                    logger.warning(f"Could not DM user {user.id} about ticket completion")
                
            else:
                # Update status to failed
                await self.bot.database.update_ticket_status(
                    ticket_data.ticket_id, 
                    TicketStatus.FAILED
                )
                
                # Send failure message to user
                embed = discord.Embed(
                    title="❌ Ticket Submission Failed",
                    description=f"Failed to submit ticket `{ticket_data.ticket_id}`. Please try again later or contact an administrator.",
                    color=discord.Color.red()
                )
                
                try:
                    await user.send(embed=embed)
                except discord.Forbidden:
                    logger.warning(f"Could not DM user {user.id} about ticket failure")
            
        except Exception as e:
            logger.error(f"Error submitting ticket {ticket_data.ticket_id}: {e}")
            
            # Update status to failed
            await self.bot.database.update_ticket_status(
                ticket_data.ticket_id, 
                TicketStatus.FAILED
            )
    
    async def on_error(self, interaction: discord.Interaction, error: Exception):
        """Handle modal errors"""
        logger.error(f"Modal error: {error}")
        
        if not interaction.response.is_done():
            await interaction.response.send_message(
                "❌ An error occurred while processing your ticket. Please try again.",
                ephemeral=True
            )
        else:
            await interaction.followup.send(
                "❌ An error occurred while processing your ticket. Please try again.",
                ephemeral=True
            )
