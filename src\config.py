"""
Configuration management for MC-Ticketer
"""
import os
from pathlib import Path
from typing import Optional
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """Application settings loaded from environment variables"""
    
    # Discord Configuration
    discord_bot_token: str = Field(..., env="DISCORD_BOT_TOKEN")
    discord_guild_id: Optional[int] = Field(None, env="DISCORD_GUILD_ID")
    command_prefix: str = Field("!", env="COMMAND_PREFIX")
    admin_role_id: Optional[int] = Field(None, env="ADMIN_ROLE_ID")
    
    # Web Scraping Configuration
    user_agent: str = Field(
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        env="USER_AGENT"
    )
    headless_mode: bool = Field(True, env="HEADLESS_MODE")
    browser_timeout: int = Field(30000, env="BROWSER_TIMEOUT")
    
    # Rate Limiting
    max_tickets_per_user_per_day: int = Field(3, env="MAX_TICKETS_PER_USER_PER_DAY")
    rate_limit_window_minutes: int = Field(60, env="RATE_LIMIT_WINDOW_MINUTES")
    
    # Logging
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_file: str = Field("logs/mc_ticketer.log", env="LOG_FILE")
    
    # Database
    database_path: str = Field("data/tickets.db", env="DATABASE_PATH")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global settings instance
settings = Settings()

# Project paths
PROJECT_ROOT = Path(__file__).parent.parent
DATA_DIR = PROJECT_ROOT / "data"
LOGS_DIR = PROJECT_ROOT / "logs"

# Ensure directories exist
DATA_DIR.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)
