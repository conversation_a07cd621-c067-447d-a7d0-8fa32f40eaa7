"""
Web scraper for Minecraft support system
"""
import asyncio
from typing import Op<PERSON>
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>
from loguru import logger
from src.config import settings
from src.database.models import TicketData
from src.scraper.anti_bot import AntiBot


class MinecraftScraper:
    """Web scraper for Minecraft support ticket submission"""
    
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        self.playwright = None
        self.anti_bot: Optional[AntiBot] = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def initialize(self):
        """Initialize the browser and page"""
        try:
            self.playwright = await async_playwright().start()
            
            # Launch browser with minimal configuration (similar to test script)
            self.browser = await self.playwright.chromium.launch(
                headless=settings.headless_mode,
                args=[
                    '--no-sandbox',
                    '--disable-http2',
                    '--disable-dev-shm-usage',
                    '--disable-gpu'
                ]
            )
            
            # Create new page with simple settings (similar to test script)
            context = await self.browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                viewport={'width': 1920, 'height': 1080},
                locale='en-US',
                timezone_id='America/New_York'
            )
            
            self.page = await context.new_page()
            
            # Set timeouts
            self.page.set_default_timeout(settings.browser_timeout)

            # Initialize anti-bot measures
            self.anti_bot = AntiBot(self.page)
            await self.anti_bot.setup_stealth_mode()

            logger.info("Browser initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize browser: {e}")
            raise
    
    async def submit_ticket(self, ticket_data: TicketData) -> bool:
        """Submit a support ticket to Minecraft help center with fallback to manual submission"""
        try:
            logger.info(f"Starting ticket submission for {ticket_data.ticket_id}")

            # Try automated submission first
            automated_success = await self.try_automated_submission(ticket_data)
            if automated_success:
                logger.info(f"Successfully submitted ticket {ticket_data.ticket_id} via automation")
                return True

            # If automation fails, provide manual submission info
            logger.info(f"Automated submission failed for {ticket_data.ticket_id}, providing manual submission info")
            return await self.provide_manual_submission_info(ticket_data)

        except Exception as e:
            logger.error(f"Error in ticket submission process for {ticket_data.ticket_id}: {e}")
            # Still try to provide manual submission info
            return await self.provide_manual_submission_info(ticket_data)
        finally:
            await self.close()

    async def try_automated_submission(self, ticket_data: TicketData) -> bool:
        """Try automated submission with quick timeout"""
        try:
            # Initialize browser if not already done
            if not self.browser:
                await self.initialize()

            # Try quick navigation (5 second timeout)
            await self.navigate_to_ticket_form_quick()

            # Check if we're on the right page
            if not await self.verify_ticket_form():
                logger.warning("Could not verify ticket form for automated submission")
                return False

            # Fill out the support form
            form_filled = await self.fill_support_form(ticket_data)
            if not form_filled:
                logger.warning("Failed to fill support form for automated submission")
                return False

            # Submit the form
            submitted = await self.submit_form()
            if not submitted:
                logger.warning("Failed to submit form for automated submission")
                return False

            return True

        except Exception as e:
            logger.warning(f"Automated submission failed: {e}")
            return False

    async def provide_manual_submission_info(self, ticket_data: TicketData) -> bool:
        """Provide manual submission information as fallback"""
        logger.info(f"Providing manual submission instructions for ticket {ticket_data.ticket_id}")

        # This counts as a "successful" submission since we're providing the user
        # with all the information they need to submit manually
        manual_info = {
            "ticket_id": ticket_data.ticket_id,
            "urls": [
                "https://help.minecraft.net/hc/en-us/request/new?ticket_form_id=360003469452",
                "https://help.minecraft.net/hc/en-us/request/new?ticket_form_id=4416074743565",
                "https://help.minecraft.net/hc/en-us/requests/new"
            ],
            "ticket_data": ticket_data
        }

        logger.info(f"Manual submission info prepared for {ticket_data.ticket_id}: {manual_info}")
        return True  # Consider this successful since we provided the info
    
    async def navigate_to_ticket_form(self):
        """Navigate directly to Minecraft ticket submission form"""
        logger.info("Navigating to Minecraft ticket submission form")

        # Try specific form URLs that we discovered
        ticket_urls = [
            "https://help.minecraft.net/hc/en-us/request/new?ticket_form_id=360003469452",  # Case Review
            "https://help.minecraft.net/hc/en-us/request/new?ticket_form_id=4416074743565",  # Report a Concern
            "https://help.minecraft.net/hc/en-us/requests/new",  # Original URL
            "https://help.minecraft.net/hc/en-us/request/new"   # Alternative URL
        ]

        # Try each URL with simple navigation strategy
        for ticket_url in ticket_urls:
            logger.info(f"Trying URL: {ticket_url}")

            try:
                # Simple navigation with reasonable timeout
                response = await self.page.goto(ticket_url, timeout=20000)

                if response and response.status < 400:
                    logger.info(f"Successfully navigated to {ticket_url} (status: {response.status})")

                    # Wait for page to load
                    await asyncio.sleep(3)

                    # Check page content
                    title = await self.page.title()
                    current_url = self.page.url
                    logger.info(f"Page title: {title}")
                    logger.info(f"Current URL: {current_url}")

                    # If we have a meaningful title or are on a form page, we're good
                    if title and title != "en-us" and len(title) > 3:
                        logger.info("Navigation successful - found meaningful page")
                        return
                    elif "request" in current_url or "form" in current_url:
                        logger.info("Navigation successful - on request/form page")
                        return
                    else:
                        logger.warning(f"Page loaded but may not be the right form: {title}")
                        # Continue to try other URLs

                else:
                    logger.warning(f"Bad response status: {response.status if response else 'No response'}")

            except Exception as e:
                logger.warning(f"Failed to navigate to {ticket_url}: {e}")
                continue

        # If we get here, all URLs failed
        raise Exception("All ticket form URLs failed to load")

    async def navigate_to_ticket_form_quick(self):
        """Quick navigation attempt with short timeout"""
        logger.info("Attempting quick navigation to ticket form")

        # Try just the main URL with a very short timeout
        ticket_url = "https://help.minecraft.net/hc/en-us/requests/new"

        try:
            response = await self.page.goto(ticket_url, timeout=5000)  # 5 second timeout

            if response and response.status < 400:
                logger.info(f"Quick navigation successful to {ticket_url}")
                await asyncio.sleep(1)  # Brief wait
                return
            else:
                raise Exception(f"Bad response status: {response.status if response else 'No response'}")

        except Exception as e:
            logger.warning(f"Quick navigation failed: {e}")
            raise

    
    async def verify_ticket_form(self) -> bool:
        """Verify we're on the ticket submission form"""
        logger.info("Verifying ticket submission form")

        try:
            # Check for common form elements
            form_indicators = [
                'form',
                'input[type="email"]',
                'textarea',
                'select',
                '[data-test-id*="ticket"]',
                '[class*="request"]',
                '[class*="form"]'
            ]

            for indicator in form_indicators:
                try:
                    element = await self.page.wait_for_selector(indicator, timeout=5000)
                    if element:
                        logger.info(f"Found form element: {indicator}")
                        return True
                except:
                    continue

            # Check URL to confirm we're on the right page
            current_url = self.page.url
            if "requests/new" in current_url:
                logger.info("URL confirms we're on ticket submission page")
                return True

            logger.warning("Could not verify ticket form presence")
            return False

        except Exception as e:
            logger.error(f"Error verifying ticket form: {e}")
            return False

    async def find_contact_support(self) -> bool:
        """Find and click contact support link"""
        logger.info("Looking for contact support option")
        
        try:
            # Look for various contact support patterns
            contact_selectors = [
                'a[href*="contact"]',
                'a[href*="support"]',
                'button:has-text("Contact")',
                'a:has-text("Contact Support")',
                'a:has-text("Contact Us")',
                '.contact-support',
                '#contact-support'
            ]
            
            for selector in contact_selectors:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=5000)
                    if element:
                        logger.info(f"Found contact element with selector: {selector}")
                        await element.click()
                        await asyncio.sleep(2)
                        return True
                except:
                    continue
            
            # If no direct contact link found, try searching for help articles
            # that might have contact support at the bottom
            await self.search_for_support_article()
            return True
            
        except Exception as e:
            logger.error(f"Error finding contact support: {e}")
            return False
    
    async def search_for_support_article(self):
        """Search for a support article that has contact support"""
        logger.info("Searching for support article with contact option")
        
        try:
            # Look for search box
            search_selectors = [
                'input[type="search"]',
                'input[placeholder*="search"]',
                '.search-input',
                '#search'
            ]
            
            for selector in search_selectors:
                try:
                    search_box = await self.page.wait_for_selector(selector, timeout=3000)
                    if search_box:
                        await search_box.fill("account help")
                        await search_box.press("Enter")
                        await asyncio.sleep(3)
                        
                        # Click on first search result
                        first_result = await self.page.wait_for_selector('a[href*="articles"]', timeout=5000)
                        if first_result:
                            await first_result.click()
                            await asyncio.sleep(2)
                        break
                except:
                    continue
                    
        except Exception as e:
            logger.error(f"Error searching for support article: {e}")
    
    async def fill_support_form(self, ticket_data: TicketData) -> bool:
        """Fill out the support form with ticket data"""
        logger.info("Filling support form")

        try:
            # Wait for form to be fully loaded
            await asyncio.sleep(3)

            # Use anti-bot measures for human-like interaction
            await self.anti_bot.random_mouse_movement()
            await self.anti_bot.human_like_delay(1000, 2000)

            # Minecraft-specific form field selectors (more comprehensive)
            form_fields = {
                'email': [
                    'input[type="email"]',
                    'input[name*="email"]',
                    '#email',
                    'input[id*="email"]',
                    'input[data-test-id*="email"]'
                ],
                'subject': [
                    'input[name*="subject"]',
                    '#subject',
                    'input[placeholder*="subject"]',
                    'input[id*="subject"]',
                    'input[data-test-id*="subject"]'
                ],
                'description': [
                    'textarea',
                    'textarea[name*="description"]',
                    '#description',
                    'textarea[id*="description"]',
                    'textarea[placeholder*="describe"]',
                    'textarea[data-test-id*="description"]'
                ],
                'name': [
                    'input[name*="name"]',
                    '#name',
                    'input[placeholder*="name"]',
                    'input[id*="name"]'
                ],
                'category': [
                    'select[name*="category"]',
                    'select[name*="type"]',
                    '#category',
                    'select[id*="category"]',
                    'select[data-test-id*="category"]'
                ]
            }
            
            # Fill email with human-like typing
            email_filled = False
            for selector in form_fields['email']:
                try:
                    email_field = await self.page.wait_for_selector(selector, timeout=5000)
                    if email_field:
                        await self.anti_bot.human_like_typing(email_field, ticket_data.email)
                        logger.info(f"Filled email field using selector: {selector}")
                        email_filled = True
                        break
                except Exception as e:
                    logger.debug(f"Email selector {selector} failed: {e}")
                    continue

            if not email_filled:
                logger.warning("Could not find email field")
            
            await self.anti_bot.human_like_delay(500, 1500)

            # Fill subject with human-like typing
            subject_filled = False
            for selector in form_fields['subject']:
                try:
                    subject_field = await self.page.wait_for_selector(selector, timeout=5000)
                    if subject_field:
                        await self.anti_bot.human_like_typing(subject_field, ticket_data.subject)
                        logger.info(f"Filled subject field using selector: {selector}")
                        subject_filled = True
                        break
                except Exception as e:
                    logger.debug(f"Subject selector {selector} failed: {e}")
                    continue

            if not subject_filled:
                logger.warning("Could not find subject field")
            
            await self.anti_bot.human_like_delay(500, 1500)

            # Fill description with human-like typing
            description_filled = False
            for selector in form_fields['description']:
                try:
                    desc_field = await self.page.wait_for_selector(selector, timeout=5000)
                    if desc_field:
                        # Build comprehensive description
                        full_description = f"Subject: {ticket_data.subject}\n\n"
                        full_description += f"Issue Type: {ticket_data.ticket_type}\n\n"
                        full_description += f"Description:\n{ticket_data.description}\n\n"

                        if ticket_data.minecraft_username:
                            full_description += f"Minecraft Username: {ticket_data.minecraft_username}\n\n"

                        full_description += "This ticket was submitted via automated system."

                        await self.anti_bot.human_like_typing(desc_field, full_description)
                        logger.info(f"Filled description field using selector: {selector}")
                        description_filled = True
                        break
                except Exception as e:
                    logger.debug(f"Description selector {selector} failed: {e}")
                    continue

            if not description_filled:
                logger.warning("Could not find description field")
            
            # Try to select category if available
            for selector in form_fields['category']:
                try:
                    category_field = await self.page.wait_for_selector(selector, timeout=3000)
                    if category_field:
                        # Try to select based on ticket type
                        await category_field.select_option(label=ticket_data.ticket_type)
                        logger.info("Selected category")
                        break
                except:
                    continue
            
            return True
            
        except Exception as e:
            logger.error(f"Error filling form: {e}")
            return False
    
    async def submit_form(self) -> bool:
        """Submit the support form"""
        logger.info("Submitting form")
        
        try:
            # Look for submit button
            submit_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:has-text("Submit")',
                'button:has-text("Send")',
                '.submit-button',
                '#submit'
            ]
            
            for selector in submit_selectors:
                try:
                    submit_button = await self.page.wait_for_selector(selector, timeout=3000)
                    if submit_button:
                        await submit_button.click()
                        logger.info("Clicked submit button")
                        
                        # Wait for submission to complete
                        await asyncio.sleep(5)
                        
                        # Check for success indicators
                        success_indicators = [
                            ':has-text("success")',
                            ':has-text("submitted")',
                            ':has-text("received")',
                            '.success',
                            '.confirmation'
                        ]
                        
                        for indicator in success_indicators:
                            try:
                                success_element = await self.page.wait_for_selector(indicator, timeout=5000)
                                if success_element:
                                    logger.info("Found success indicator")
                                    return True
                            except:
                                continue
                        
                        # If no success indicator found, assume success if no error
                        return True
                except:
                    continue
            
            logger.error("Could not find submit button")
            return False
            
        except Exception as e:
            logger.error(f"Error submitting form: {e}")
            return False
    
    async def close(self):
        """Close browser and cleanup"""
        try:
            if self.page:
                await self.page.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
            
            logger.info("Browser closed successfully")
            
        except Exception as e:
            logger.error(f"Error closing browser: {e}")
