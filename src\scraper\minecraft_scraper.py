"""
Web scraper for Minecraft support system
"""
import asyncio
from typing import Op<PERSON>
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>
from loguru import logger
from src.config import settings
from src.database.models import TicketData
from src.scraper.anti_bot import AntiBot


class MinecraftScraper:
    """Web scraper for Minecraft support ticket submission"""
    
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        self.playwright = None
        self.anti_bot: Optional[AntiBot] = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def initialize(self):
        """Initialize the browser and page"""
        try:
            self.playwright = await async_playwright().start()
            
            # Launch browser
            self.browser = await self.playwright.chromium.launch(
                headless=settings.headless_mode,
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            # Create new page with realistic settings
            context = await self.browser.new_context(
                user_agent=settings.user_agent,
                viewport={'width': 1920, 'height': 1080},
                locale='en-US',
                timezone_id='America/New_York'
            )
            
            self.page = await context.new_page()
            
            # Set timeouts
            self.page.set_default_timeout(settings.browser_timeout)

            # Initialize anti-bot measures
            self.anti_bot = AntiBot(self.page)
            await self.anti_bot.setup_stealth_mode()

            logger.info("Browser initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize browser: {e}")
            raise
    
    async def submit_ticket(self, ticket_data: TicketData) -> bool:
        """Submit a support ticket to Minecraft help center"""
        try:
            logger.info(f"Starting ticket submission for {ticket_data.ticket_id}")
            
            # Initialize browser if not already done
            if not self.browser:
                await self.initialize()
            
            # Navigate to Minecraft help center
            await self.navigate_to_help_center()
            
            # Find and navigate to contact support
            contact_found = await self.find_contact_support()
            if not contact_found:
                logger.error("Could not find contact support option")
                return False
            
            # Fill out the support form
            form_filled = await self.fill_support_form(ticket_data)
            if not form_filled:
                logger.error("Failed to fill support form")
                return False
            
            # Submit the form
            submitted = await self.submit_form()
            if not submitted:
                logger.error("Failed to submit form")
                return False
            
            logger.info(f"Successfully submitted ticket {ticket_data.ticket_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error submitting ticket {ticket_data.ticket_id}: {e}")
            return False
        finally:
            await self.close()
    
    async def navigate_to_help_center(self):
        """Navigate to Minecraft help center"""
        logger.info("Navigating to Minecraft help center")
        
        # Try the main help center URL
        await self.page.goto("https://help.minecraft.net", wait_until="networkidle")
        
        # Wait for page to load
        await asyncio.sleep(2)
        
        # Check if we're on the right page
        title = await self.page.title()
        logger.info(f"Page title: {title}")
    
    async def find_contact_support(self) -> bool:
        """Find and click contact support link"""
        logger.info("Looking for contact support option")
        
        try:
            # Look for various contact support patterns
            contact_selectors = [
                'a[href*="contact"]',
                'a[href*="support"]',
                'button:has-text("Contact")',
                'a:has-text("Contact Support")',
                'a:has-text("Contact Us")',
                '.contact-support',
                '#contact-support'
            ]
            
            for selector in contact_selectors:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=5000)
                    if element:
                        logger.info(f"Found contact element with selector: {selector}")
                        await element.click()
                        await asyncio.sleep(2)
                        return True
                except:
                    continue
            
            # If no direct contact link found, try searching for help articles
            # that might have contact support at the bottom
            await self.search_for_support_article()
            return True
            
        except Exception as e:
            logger.error(f"Error finding contact support: {e}")
            return False
    
    async def search_for_support_article(self):
        """Search for a support article that has contact support"""
        logger.info("Searching for support article with contact option")
        
        try:
            # Look for search box
            search_selectors = [
                'input[type="search"]',
                'input[placeholder*="search"]',
                '.search-input',
                '#search'
            ]
            
            for selector in search_selectors:
                try:
                    search_box = await self.page.wait_for_selector(selector, timeout=3000)
                    if search_box:
                        await search_box.fill("account help")
                        await search_box.press("Enter")
                        await asyncio.sleep(3)
                        
                        # Click on first search result
                        first_result = await self.page.wait_for_selector('a[href*="articles"]', timeout=5000)
                        if first_result:
                            await first_result.click()
                            await asyncio.sleep(2)
                        break
                except:
                    continue
                    
        except Exception as e:
            logger.error(f"Error searching for support article: {e}")
    
    async def fill_support_form(self, ticket_data: TicketData) -> bool:
        """Fill out the support form with ticket data"""
        logger.info("Filling support form")
        
        try:
            # Common form field selectors
            form_fields = {
                'email': ['input[type="email"]', 'input[name*="email"]', '#email'],
                'subject': ['input[name*="subject"]', '#subject', 'input[placeholder*="subject"]'],
                'description': ['textarea', 'textarea[name*="description"]', '#description'],
                'name': ['input[name*="name"]', '#name', 'input[placeholder*="name"]'],
                'category': ['select[name*="category"]', 'select[name*="type"]', '#category']
            }
            
            # Fill email
            for selector in form_fields['email']:
                try:
                    email_field = await self.page.wait_for_selector(selector, timeout=3000)
                    if email_field:
                        await email_field.fill(ticket_data.email)
                        logger.info("Filled email field")
                        break
                except:
                    continue
            
            # Fill subject
            for selector in form_fields['subject']:
                try:
                    subject_field = await self.page.wait_for_selector(selector, timeout=3000)
                    if subject_field:
                        await subject_field.fill(ticket_data.subject)
                        logger.info("Filled subject field")
                        break
                except:
                    continue
            
            # Fill description
            for selector in form_fields['description']:
                try:
                    desc_field = await self.page.wait_for_selector(selector, timeout=3000)
                    if desc_field:
                        full_description = f"{ticket_data.description}\n\n"
                        if ticket_data.minecraft_username:
                            full_description += f"Minecraft Username: {ticket_data.minecraft_username}\n"
                        full_description += f"Ticket Type: {ticket_data.ticket_type}"
                        
                        await desc_field.fill(full_description)
                        logger.info("Filled description field")
                        break
                except:
                    continue
            
            # Try to select category if available
            for selector in form_fields['category']:
                try:
                    category_field = await self.page.wait_for_selector(selector, timeout=3000)
                    if category_field:
                        # Try to select based on ticket type
                        await category_field.select_option(label=ticket_data.ticket_type)
                        logger.info("Selected category")
                        break
                except:
                    continue
            
            return True
            
        except Exception as e:
            logger.error(f"Error filling form: {e}")
            return False
    
    async def submit_form(self) -> bool:
        """Submit the support form"""
        logger.info("Submitting form")
        
        try:
            # Look for submit button
            submit_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:has-text("Submit")',
                'button:has-text("Send")',
                '.submit-button',
                '#submit'
            ]
            
            for selector in submit_selectors:
                try:
                    submit_button = await self.page.wait_for_selector(selector, timeout=3000)
                    if submit_button:
                        await submit_button.click()
                        logger.info("Clicked submit button")
                        
                        # Wait for submission to complete
                        await asyncio.sleep(5)
                        
                        # Check for success indicators
                        success_indicators = [
                            ':has-text("success")',
                            ':has-text("submitted")',
                            ':has-text("received")',
                            '.success',
                            '.confirmation'
                        ]
                        
                        for indicator in success_indicators:
                            try:
                                success_element = await self.page.wait_for_selector(indicator, timeout=5000)
                                if success_element:
                                    logger.info("Found success indicator")
                                    return True
                            except:
                                continue
                        
                        # If no success indicator found, assume success if no error
                        return True
                except:
                    continue
            
            logger.error("Could not find submit button")
            return False
            
        except Exception as e:
            logger.error(f"Error submitting form: {e}")
            return False
    
    async def close(self):
        """Close browser and cleanup"""
        try:
            if self.page:
                await self.page.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
            
            logger.info("Browser closed successfully")
            
        except Exception as e:
            logger.error(f"Error closing browser: {e}")
